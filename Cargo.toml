[package]
name = "iec-61131-3-lsp"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
description = ""
version = "0.1.0"
edition = "2021"
publish = false

[workspace]
members = [
    "crates/ast",
    "crates/db",
    "crates/server",
    "crates/tree-sitter",
    "vscode/server",
]

[workspace.dependencies]
auto-lsp = "0.6.2"
auto-lsp-codegen = "0.2.0"
log = "0.4.27"
dashmap = "6.1.0"
rustc-hash = "2.1.0"
topiary-core = "0.6.1"
ast = { path = "crates/ast" }
tree-sitter-iec-61131-3 = { path = "crates/tree-sitter" }
server = { path = "crates/server" }
db = { path = "crates/db" }
bon = "3.6.4"
phf = { version = "0.11.3", features = ["macros"] }
bitflags = "2.9.1"
