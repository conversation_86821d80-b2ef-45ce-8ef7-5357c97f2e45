/*
This file is part of tree-sitter-iec-61131-3.
Copyright (C) 2025 CLAUZEL Adrien

auto-lsp is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>
*/


/**
 * @file Iec611313 grammar for tree-sitter
 * <AUTHOR> Adrien <clauzel<PERSON><PERSON>@mail.com>
 * @license GPL-3.0
 */

function commaSep1(rule) {
    return seq(rule, repeat(seq(',', rule)))
}

function commaSep(rule) {
    return optional(commaSep1(rule))
}

const RESERVED_NAMES = [
    "PROGRAM", "END_PROGRAM",
    "CONFIGURATION", "END_CONFIGURATION",
    "RESOURCE", "END_RESOURCE",
    "NAMESPACE", "END_NAMESPACE",
    "USING",
    "CLASS", "END_CLASS",
    "INTERFACE", "END_INTERFACE",
    "FUNCTION", "END_FUNCTION",
    "FUNCTION_BLOCK", "END_FUNCTION_BLOCK",
    "TYPE", "END_TYPE",
    "VAR", "END_VAR",
    "VAR_INPUT",
    "VAR_OUTPUT",
    "VAR_IN_OUT",
    "VAR_TEMP",
    "VAR_EXTERNAL",
    "VAR_GLOBAL",
    "IF", "THEN", "ELSE", "END_IF",
    "CASE", "OF", "END_CASE",
    "FOR", "TO", "BY", "END_FOR",
    "REPEAT", "UNTIL", "END_REPEAT",
    "WHILE", "END_WHILE",
    "DO",
    "EXIT", "RETURN",
];

const io_var_decls = $ => [
    $.input_decls,
    $.output_decls,
    $.in_out_decls
]

const func_var_decls = $ => [
    $.external_var_decls,
    $.var_decls
]

const other_var_decls = $ => [
    $.retain_var_decls,
    $.no_retain_var_decls,
    $.loc_partly_var_decl
]

const PREC = {
    expression: 11,
    parameter_list: 10, // _ (parameter_list)
    dereference: 9, // ^
    unary: 8, // + - NOT
    exponentiation: 7, // **
    multiply: 6, // *
    divide: 6, // /
    modulo: 6, // MOD
    add: 5, // +
    substract: 5, // -
    comparison: 4, // < > <= >=
    equality: 4, // = <>
    boolean_and: 3, // & AND
    boolean_xor: 2, // XOR
    boolean_or: 1 // OR
}

/// <reference types="tree-sitter-cli/dsl" />
// @ts-check
module.exports = grammar({
    name: "iec_61131_3",

    extras: $ => [
        /\s/, // Whitespace
        $.comment,
        token(choice('\t', '\r', '\n')),
    ],

    reserved: {
        global: $ => RESERVED_NAMES,
    },

    supertypes: $ => [
        $._func_variables,
        $._fb_variables,
        $._class_variables,
        $._method_variables,

        $._input_var_kind,
        $._fb_input_var_kind,
        $._output_var_kind,
        $._fb_output_var_kind,
        $._temp_var_kind,
        $._in_out_var_kind,
        $._external_var_kind,
        $._global_var_kind,

        $._var_decl,
        $._var_decl_init,

        $._data_type_access,
        $._elem_type_name
    ],

    precedences: $ => [
        [$.global_ref_deref, $.enum_value],
        [$.string_type_name, $.s_byte_str_spec],
        [$.string_type_name, $.d_byte_str_spec]
    ],

    conflicts: $ => [
        [$.variable_list, $.fb_name],
        [$.ref_name, $.param_assign],
        [$.local_variable, $.multi_elem_var],
        [$.this_variable, $.multi_elem_var],

        [$.signed_int],

        // local variable declarations
        [$.var_decls, $.loc_var_decls],
        [$.var_decls, $.loc_var_decls, $.loc_partly_var_decl],
        [$.var_decls, $.loc_partly_var_decl],

        [$.retain_var_decls, $.loc_var_decls, $.loc_partly_var_decl],
        [$.retain_var_decls, $.loc_partly_var_decl],
        [$.no_retain_var_decls, $.loc_var_decls, $.loc_partly_var_decl],
        [$.no_retain_var_decls, $.loc_partly_var_decl],

        // conflicts in type declarations
        [$.enum_spec_init, $.enum_value_spec],
        [$.numeric_literal, $.enum_value_spec],
        [$.numeric_type_name, $.subrange_spec],
        [$.subrange_spec, $.enum_spec_init],

        [$.global_ref_deref],
    ],

    word: $ => $.identifier,

    rules: {
        // Source file declaration
        source_file: $ => repeat(
            choice(
                $.config_decl, // Declaration of CONFIGURATION and RESOURCE
                $.prog_decl, // Declaration of PROGRAM
                $.namespace_decl, // Declaration of NAMESPACE (including all other declarations)
            )
        ),

        // Table 3 - Comments 

        comment: $ => choice(
            seq('//', /[^\r\n]*/),
            seq('(*', repeat(choice(/[^*]/, /\*[^)]/)), '*)'),
            seq('/*', repeat(choice(/[^*]/, /\*[^/]/)), '*/')
        ),

        // Table 4 - Pragma 

        pragma: $ => seq('{', repeat(choice(/[^}]/, /}[^}]/)), '}'),

        // Table 5 - Numeric literal

        constant: $ => choice(
            $.numeric_literal,
            $.char_literal,
            $.time_literal,
            $.bool_literal
        ),

        numeric_literal: $ => choice(
            $.int_literal,
            $.real_literal
        ),

        int_literal: $ => seq(
            optional(seq(field("kind", $.int_kind), "#")),
            field("int", choice(
                $.signed_int,
                $.binary_int,
                $.octal_int,
                $.hex_int
            ))
        ),

        int_kind: $ => choice(
            $.multibits_type_name,
            $.int_type_name
        ),

        unsigned_int: $ => /[0-9]+(_[0-9]+)*/,

        signed_int: $ => seq(
            optional(choice('+', '-')),
            $.unsigned_int
        ),

        binary_int: $ => seq(
            '2#',
            $.bit,
            repeat1(seq(optional('_'), $.bit))
        ),

        octal_int: $ => seq(
            '8#',
            $.octal_digit,
            repeat1(seq(optional('_'), $.octal_digit))
        ),

        hex_int: $ => seq(
            '16#',
            $.hex_digit,
            repeat1(seq(optional('_'), $.hex_digit))
        ),

        real_literal: $ => choice(
            $.real,
            $.l_real
        ),

        real: $ => seq(
            "REAL",
            field("value", $.real_value)
        ),

        l_real: $ => seq(
            "LREAL",
            field("value", $.real_value)
        ),

        real_value: $ => /[0-9]+(_[0-9]+)*/,

        bool_literal: $ => seq(
            field("type", optional(seq($.bool_type_name, '#'))),
            // fixme! Add support for numbers in auto-lsp
            field("value", choice('TRUE', 'FALSE'))
        ),

        // Table 6 - Character String literals
        // Table 7 - Two-character combinations in character strings

        char_literal: $ => seq(
            optional('STRING#'),
            field("char", $.char_str)
        ),

        char_str: $ => choice(
            $.s_byte_char_str,
            $.d_byte_char_str
        ),

        s_byte_char_str: $ => seq(
            "'",
            field("char", $.s_byte_char_value),
            "'"
        ),

        d_byte_char_str: $ => seq(
            '"',
            field("char", $.d_byte_char_value),
            '"'
        ),

        s_byte_char_value: $ => choice(
            $.common_char_value,
            '$\'',
            '"',
            seq('$', $.hex_digit, $.hex_digit)
        ),

        d_byte_char_value: $ => choice(
            $.common_char_value,
            "'",
            '$"',
            seq('$', $.hex_digit, $.hex_digit, $.hex_digit, $.hex_digit)
        ),

        common_char_value: $ => choice(
            ' ',
            '!',
            '#',
            '%',
            '&',
            ...Array.from({ length: 11 }, (_, i) => String.fromCharCode(40 + i)), // '('..'/'
            ...Array.from({ length: 10 }, (_, i) => String.fromCharCode(48 + i)), // '0'..'9'
            ...Array.from({ length: 7 }, (_, i) => String.fromCharCode(58 + i)),  // ':'..'@'
            ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)), // 'A'..'Z'
            ...Array.from({ length: 6 }, (_, i) => String.fromCharCode(91 + i)),  // '['..'`'
            ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(97 + i)), // 'a'..'z'
            ...Array.from({ length: 4 }, (_, i) => String.fromCharCode(123 + i)), // '{'..'~'
            '$$',
            '$L',
            '$N',
            '$P',
            '$R',
            '$T'
        ),

        // Table 8 - Duration literals
        // Table 9 – Date and time of day literals 

        time_literal: $ => choice(
            $.duration,
            $.time_of_day,
            $.date,
            $.date_and_time
        ),

        duration: $ => choice(
            $.time,
            $.ltime
        ),

        time: $ => seq(
            choice('T', 'TIME'),
            '#',
            field("sign", optional(choice('+', '-'))),
            field("value", $.time_value)
        ),

        ltime: $ => seq(
            choice('LT', 'LTIME'),
            '#',
            field("sign", optional(choice('+', '-'))),
            field("value", $.time_value)
        ),

        time_value: $ => /([0-9._]+[dhms])+/,

        fix_point: $ => seq(
            field("real", $.unsigned_int),
            '.',
            field("frac", $.unsigned_int)
        ),

        time_of_day: $ => choice(
            $.tod,
            $.ltod
        ),

        tod: $ => seq(
            choice('TOD', 'TIME_OF_DAY'),
            '#',
            field("value", $.daytime)
        ),

        ltod: $ => seq(
            choice('LTOD', 'LTIME_OF_DAY'),
            '#',
            field("value", $.daytime)
        ),

        daytime: $ => /[0-9]{2}:[0-9]{2}:[0-9]{2}/,

        date: $ => choice(
            $.short_date,
            $.long_date
        ),

        short_date: $ => seq(
            choice('D', 'DATE'),
            '#',
            field("value", $.date_literal)
        ),

        long_date: $ => seq(
            choice('LD', 'LDATE'),
            '#',
            field("value", $.date_literal)
        ),

        date_literal: $ => /[0-9]{4}-[0-9]{2}-[0-9]{2}/,

        date_and_time: $ => choice(
            $.short_date_and_time,
            $.long_date_and_time
        ),

        short_date_and_time: $ => seq(
            choice('DT', 'DATE_AND_TIME'),
            '#',
            field("value", $.date_and_daytime)
        ),

        long_date_and_time: $ => seq(
            choice('LDT', 'LDATE_AND_TIME'),
            '#',
            field("value", $.date_and_daytime)
        ),

        date_and_daytime: $ => /[0-9]{4}-[0-9]{2}-[0-9]{2}[0-9]{2}:[0-9]{2}:[0-9]{2}/,

        // Table 10 - Elementary data types

        _data_type_access: $ => choice(
            $.numeric_type_name,
            $.bit_str_type_name,
            $.string_type_name,
            $.date_type_name,
            $.time_type_name,
            $.type_access
        ),

        _elem_type_name: $ => choice(
            $.numeric_type_name,
            $.bit_str_type_name,
            $.string_type_name,
            $.date_type_name,
            $.time_type_name,
        ),

        numeric_type_name: $ => choice(
            $.int_type_name,
            $.real_type_name
        ),

        int_type_name: $ => choice(
            $.sign_int_type_name,
            $.unsign_int_type_name
        ),

        sign_int_type_name: $ => choice(
            'SINT',
            'INT',
            'DINT',
            'LINT'
        ),

        unsign_int_type_name: $ => choice(
            'USINT',
            'UINT',
            'UDINT',
            'ULINT'
        ),

        real_type_name: $ => choice(
            'REAL',
            'LREAL'
        ),

        string_type_name: $ => choice(
            seq('STRING', optional(seq('[', $.unsigned_int, ']'))),
            seq('WSTRING', optional(seq('[', $.unsigned_int, ']')))
        ),

        time_type_name: $ => choice(
            'TIME',
            'LTIME'
        ),

        date_type_name: $ => choice(
            'DATE',
            'LDATE'
        ),

        tod_type_name: $ => choice(
            'TIME_OF_DAY',
            'TOD',
            'LTOD'
        ),

        dt_type_name: $ => choice(
            'DATE_AND_TIME',
            'DT',
            'LDT'
        ),

        bit_str_type_name: $ => choice(
            $.bool_type_name,
            $.multibits_type_name
        ),

        bool_type_name: $ => 'BOOL',

        multibits_type_name: $ => choice(
            'BYTE',
            'WORD',
            'DWORD',
            'LWORD'
        ),

        // Table 11 - Declaration of user-defined data types and initialization

        type_access: $ => seq(
            field("qualifier", optional(seq($.namespace_qualifier, "::"))),
            field("access", $.identifier)
        ),

        data_type_decl: $ => seq(
            'TYPE',
            repeat(seq($.type_decl, ';')),
            'END_TYPE'
        ),

        type_decl: $ => seq(
            field("name", $.identifier),
            field("declaration", choice(
                //$.simple_type_decl,
                //$.subrange_type_decl,
                //$.enum_type_decl,
                //$.array_type_decl,
                //$.struct_type_decl,
                //$.str_type_decl,
                //$.ref_type_decl,
            )),
            field("spec", optional(
                choice(
                    $.identifier,
                    $.array_spec,
                    $.struct_spec_init,
                    $.subrange_spec_init
                )
            ))
        ),

        spec: $ => seq(
            $.type_access,
            choice(
                $.array_spec,
                $.identifier,
                $.struct_spec_init,
                $.subrange_spec_init
            )
        ),

        simple_type_decl: $ => seq(':', $.simple_spec_init),

        simple_spec_init: $ => prec.left(seq(seq(':=', $.constant_expr))),

        subrange_type_decl: $ => seq(':', $.subrange_spec_init),

        subrange_spec_init: $ => prec.left(seq(
            $.subrange_spec,
            optional(seq(':=', $.signed_int))
        )),

        subrange_spec: $ => choice(
            seq($.int_type_name, '(', $.subrange, ')'),
            $.type_access
        ),

        subrange: $ => seq(
            field("lower", $.constant_expr),
            '..',
            field("upper", $.constant_expr)
        ),

        enum_type_decl: $ => seq(
            ':',
            choice(seq(optional($._elem_type_name), $.named_spec_init), $.enum_spec_init)
        ),

        named_spec_init: $ => prec.left(seq(
            '(', commaSep1($.enum_value_spec), ')',
            optional(seq(':=', $.enum_value))
        )),

        enum_spec_init: $ => prec.left(seq(
            choice(
                seq('(', commaSep($.identifier), ')'),
                $.type_access
            ),
            optional(seq(':=', $.enum_value))
        )),

        enum_value_spec: $ => seq(
            $.identifier,
            optional(seq(
                ':=',
                choice($.int_literal, $.constant_expr)
            ))
        ),

        enum_value: $ => seq(
            "#",
            $.identifier
        ),

        array_type_decl: $ => seq(':', $.array_spec_init),

        array_spec_init: $ => prec.left(seq(
            field("spec", $.array_spec),
            optional(seq(':=', $.array_init))
        )),

        array_spec: $ => seq(
            'ARRAY', '[', field("ranges", commaSep1($.subrange)), ']',
            'OF',
            field("type", $._data_type_access)),

        array_init: $ => seq('[', commaSep($.array_elem_init), ']'),

        array_elem_init: $ => choice(
            $.array_elem_init_value,
            seq($.unsigned_int, '(', optional($.array_elem_init_value), ')')
        ),

        array_elem_init_value: $ => choice(
            $.constant_expr,
            $.enum_value,
            $.struct_init,
            $.array_init
        ),

        struct_type_decl: $ => seq(':', $.struct_decl),
        struct_spec_init: $ => seq(':=', $.struct_init),

        struct_decl: $ => seq(
            'STRUCT',
            optional('OVERLAP'),
            repeat1(seq($.struct_elem_decl, ';')),
            'END_STRUCT'
        ),

        struct_elem_decl: $ => seq(
            field("name", $.identifier),
            optional(seq(
                $.located_at,
                optional($.multibit_part_access)
            )),
            ':',
            choice(
                $.simple_spec_init,
                $.subrange_spec_init,
                $.enum_spec_init,
                $.array_spec_init,
                $.struct_spec_init
            )
        ),

        struct_init: $ => seq('(', commaSep($.struct_elem_init), ')'),

        struct_elem_init: $ => seq(
            field("name", $.identifier),
            ':=',
            choice($.constant_expr, $.enum_value, $.array_init, $.struct_init, $.ref_value)
        ),

        str_type_decl: $ => prec.left(seq(
            $.string_type_name,
            ':',
            $.string_type_name,
            optional(seq(':=', $.char_str))
        )),

        // Table 16 - Directly represented variables 

        direct_variable: $ => prec.left(seq(
            '%',
            choice('I', 'Q', 'M'),
            optional(choice('X', 'B', 'W', 'D', 'L')),
            $.unsigned_int,
            repeat(seq('.', $.unsigned_int))
        )),

        // Table 12 - Reference operations 

        ref_type_decl: $ => seq(
            field("name", $.identifier),
            ':',
            $.ref_spec_init
        ),

        ref_spec_init: $ => prec.left(seq(
            $.ref_spec,
            optional(seq(':=', $.ref_value))
        )),

        ref_spec: $ => seq(
            'REF_TO',
            $._data_type_access
        ),

        ref_name: $ => $.identifier,

        ref_value: $ => choice(
            $.ref_addr,
            'NULL'
        ),

        ref_addr: $ => seq(
            'REF',
            '(',
            choice($.symbolic_variable, $.instance_name),
            ')'
        ),

        ref_assign: $ => seq(
            $.ref_name,
            ':=',
            choice($.ref_name, $.ref_deref, $.ref_value)
        ),

        ref_deref: $ => (
            $.ref_name,
            prec(PREC.dereference, '^')
        ),

        // Table 13 - Declaration of variables/Table 14 – Initialization of variables 

        variable: $ => choice($.direct_variable, $.symbolic_variable),

        symbolic_variable: $ => choice(
            $.local_variable,
            $.this_variable,
        ),

        local_variable: $ => choice(
            //$.identifier,
            $.ref_deref,
            $.multi_elem_var
        ),

        this_variable: $ => seq('THIS', '.',
            choice(
                $.identifier,
                $.ref_deref,
                $.multi_elem_var
            )
        ),

        multi_elem_var: $ => seq(
            $.ref_deref,
            prec.left(repeat1(choice($.subscript_list, $.struct_variable)))
        ),

        subscript_list: $ => seq('[', commaSep($.subscript), ']'),

        subscript: $ => $.expression,

        struct_variable: $ => seq(
            '.',
            $.struct_elem_select
        ),

        struct_elem_select: $ => $.ref_deref,

        input_decls: $ => seq(
            'VAR_INPUT',
            field("retain", optional(choice('RETAIN', 'NON_RETAIN'))),
            repeat(seq($.input_var, optional(';'))),
            'END_VAR'
        ),

        input_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._input_var_kind)
        ),

        _input_var_kind: $ => choice($._var_decl_init, $.edge_decl, $.array_conform_decl),

        edge_decl: $ => seq(
            field("variables", $.variable_list),
            ':',
            'BOOL',
            field("edge", optional(choice('R_EDGE', 'F_EDGE')))
        ),

        // : Variable_List ':' ( Simple_Spec_Init | Str_Var_Decl | Ref_Spec_Init )
        //| Array_Var_Decl_Init | Struct_Var_Decl_Init | FB_Decl_Init | Interface_Spec_Init; 

        // INPUTS
        // OUTPUTS
        // VARS
        // RETAIN
        _var_decl_init: $ => choice(
            $.simple_spec_init,
            $.str_var_decl,
            $.ref_spec_init,
            $.array_spec_init,
            $.struct_spec_init,
            $.interface_spec_init
        ),

        interface_spec_init: $ => seq(':=', $.interface_value),

        // ( Simple_Spec | Str_Var_Decl | Array_Var_Decl | Struct_Var_Decl )

        // INOUT
        // TEMP
        _var_decl: $ => choice(
            $.type_access,
            $.str_var_decl,
            $.array_spec,
        ),

        variable_list: $ => commaSep1($.identifier),

        array_conformand: $ => seq(
            'ARRAY',
            '[',
            commaSep1('*'),
            ']',
            'OF',
            $._data_type_access
        ),

        array_conform_decl: $ => seq(
            field("variables", $.variable_list),
            ':',
            $.array_conformand
        ),

        fb_decl_no_init: $ => seq(
            commaSep1($.fb_name),
            ':',
            $.type_access
        ),

        fb_decl_init: $ => seq(':=', $.struct_init),

        fb_name: $ => $.identifier,

        output_decls: $ => seq(
            'VAR_OUTPUT',
            optional(choice('RETAIN', 'NON_RETAIN')),
            repeat(seq($.output_var, optional(';'))),
            'END_VAR'
        ),

        output_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._output_var_kind)
        ),

        _output_var_kind: $ => choice($._var_decl_init, $.array_conform_decl),

        in_out_decls: $ => seq(
            'VAR_IN_OUT',
            repeat(seq($.in_out_var, optional(';'))),
            'END_VAR'
        ),

        in_out_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._in_out_var_kind)
        ),

        _in_out_var_kind: $ => choice($._var_decl, $.array_conform_decl, $.fb_decl_no_init),

        var_decls: $ => seq(
            'VAR',
            field("constant", optional('CONSTANT')),
            field("access", optional($.access_spec)),
            repeat(seq($.var_decl_init_list, optional(';'))),
            'END_VAR'
        ),

        retain_var_decls: $ => seq(
            'VAR',
            field("retain", 'RETAIN'),
            field("access", optional($.access_spec)),
            repeat(seq($.var_decl_init_list, optional(';'))),
            'END_VAR'
        ),

        var_decl_init_list: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._var_decl)
        ),

        loc_var_decls: $ => seq(
            'VAR',
            field("constant_or_retain", optional(choice('CONSTANT', 'RETAIN', 'NON_RETAIN'))),
            repeat(seq($.loc_var_decl, optional(';'))),
            'END_VAR'
        ),

        loc_var_decl: $ => seq(
            optional(field("variable_name", $.identifier)),
            $.located_at,
            ':',
            $.loc_var_spec_init
        ),

        temp_var_decls: $ => seq(
            'VAR_TEMP',
            repeat(seq($.temp_var, optional(';'))),
            'END_VAR'
        ),

        temp_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._temp_var_kind)
        ),

        _temp_var_kind: $ => choice($._var_decl, $.ref_spec),

        external_var_decls: $ => seq(
            'VAR_EXTERNAL',
            field("constant", optional('CONSTANT')),
            repeat(seq($.external_decl, optional(';'))),
            'END_VAR'
        ),

        external_decl: $ => seq(
            field("name", $.identifier),
            ':',
            field("type", $._external_var_kind)
        ),

        _external_var_kind: $ => choice($._var_decl, $.array_conform_decl),

        global_var_decls: $ => seq(
            'VAR_GLOBAL',
            field("constant_or_retain", optional(choice('CONSTANT', 'RETAIN'))),
            repeat(seq($.global_var_decl, optional(';'))),
            'END_VAR'
        ),

        global_var_decl: $ => seq(
            field("spec", $.global_var_spec),
            ':',
            field("type", $._global_var_kind)
        ),

        _global_var_kind: $ => choice($.loc_var_spec_init, $.type_access),

        global_var_spec: $ => choice(
            seq(commaSep1($.identifier)),
            seq(
                $.identifier,
                $.located_at
            )
        ),

        loc_var_spec_init: $ => choice(
            $.simple_spec_init,
            $.array_spec_init,
            $.struct_spec_init,
            $.s_byte_str_spec,
            $.d_byte_str_spec,
        ),

        located_at: $ => seq(
            'AT',
            $.direct_variable
        ),

        str_var_decl: $ => choice(
            $.s_byte_str_spec,
            $.d_byte_str_spec
        ),

        s_byte_str_spec: $ => prec.left(seq(
            'STRING',
            optional(seq('[', field("size", $.unsigned_int), ']')),
            optional(seq(':=', field("value", $.s_byte_char_str)))
        )),

        d_byte_str_spec: $ => prec.left(seq(
            'WSTRING',
            optional(seq('[', field("size", $.unsigned_int), ']')),
            optional(seq(':=', field("value", $.d_byte_char_str)))
        )),

        loc_partly_var_decl: $ => seq(
            'VAR',
            field("retain", optional(choice('RETAIN', 'NON_RETAIN'))),
            repeat($.loc_partly_var),
            'END_VAR'
        ),

        loc_partly_var: $ => seq(
            field("variable_name", $.identifier),
            'AT',
            '%',
            choice('I', 'Q', 'M'),
            '*',
            ':',
            $.var_spec,
            optional(';')
        ),

        var_spec: $ => choice(
            //$.identifier,
            $.array_spec,
            $.type_access,
            seq(choice('STRING', 'WSTRING'), optional(seq('[', $.unsigned_int, ']'))),
        ),

        // Table 19 - Function declaration

        func_decl: $ => seq(
            'FUNCTION',
            field("name", $.identifier),
            field("access", optional(seq(':', $._data_type_access))),
            field("directives", repeat($.using_directive)),
            field("variables", repeat($._func_variables)),
            field("body", optional($.func_body)),
            'END_FUNCTION'
        ),

        _func_variables: $ => choice(
            ...io_var_decls($),
            ...func_var_decls($),
            $.temp_var_decls,
        ),

        func_body: $ => choice(
            $.ladder_diagram,
            $.fb_diagram,
            $.stmt_list,
        ),

        // Table 40 – Function block type declaration
        // Table 41 - Function block instance declaration

        fb_decl: $ => seq(
            'FUNCTION_BLOCK',
            field("qualifier", optional(choice('FINAL', 'ABSTRACT'))),
            field("name", $.identifier),
            field("directives", repeat($.using_directive)),
            field("extends", optional(seq("EXTENDS", $.type_access))),
            field("implements", optional(seq("IMPLEMENTS", $.interface_name_list))),
            field("variables", repeat($._fb_variables)),
            field("method", repeat($.method_decl)),
            field("body", optional($.fb_body)),
            "END_FUNCTION_BLOCK"
        ),

        _fb_variables: $ => choice(
            $.fb_input_decls,
            $.fb_output_decls,
            $.in_out_decls,
            $.temp_var_decls,
            ...func_var_decls($),
            ...other_var_decls($)
        ),

        fb_input_decls: $ => seq(
            'VAR_INPUT',
            optional(choice('RETAIN', 'NON_RETAIN')),
            repeat(seq($.fb_input_var, optional(';'))),
            'END_VAR'
        ),

        fb_input_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._fb_input_var_kind)
        ),

        _fb_input_var_kind: $ => choice(
            $._var_decl_init,
            $.edge_decl,
            $.array_conform_decl
        ),

        fb_output_decls: $ => seq(
            'VAR_OUTPUT',
            optional(choice('RETAIN', 'NON_RETAIN')),
            repeat(seq($.fb_output_var, optional(';'))),
            'END_VAR'
        ),

        fb_output_var: $ => seq(
            field("variables", $.variable_list),
            ':',
            field("type", $._fb_output_var_kind)
        ),

        _fb_output_var_kind: $ => choice(
            $._var_decl_init,
            $.array_conform_decl
        ),

        no_retain_var_decls: $ => seq(
            'VAR', 'NON_RETAIN',
            field("spec", optional($.access_spec)),
            repeat(seq($.var_decl_init_list, optional(';'))),
            'END_VAR'
        ),

        fb_body: $ => choice(
            $.SFC,
            $.ladder_diagram,
            $.fb_diagram,
            $.stmt_list,
        ),

        method_decl: $ => seq(
            'METHOD',
            $.access_spec,
            optional(choice('FINAL', 'ABSTRACT')),
            optional('OVERRIDE'),
            $.identifier,
            optional(seq(':', $._data_type_access)),
            repeat(choice(...io_var_decls($), ...func_var_decls($), $.temp_var_decls)),
            field("body", optional($.func_body)),
            'END_METHOD'
        ),

        // Table 48 - Class
        // Table 50 Textual call of methods – Formal and non-formal parameter list 

        class_decl: $ => seq(
            'CLASS',
            field("qualifier", optional(choice('FINAL', 'ABSTRACT'))),
            field("name", $.class_type_name),
            field("directives", repeat($.using_directive)),
            field("extends", optional(seq("EXTENDS", $.type_access))),
            field("implements", optional(seq("IMPLEMENTS", $.interface_name_list))),
            field("declarations", repeat($._class_variables)),
            field("method", repeat($.method_decl)),
            'END_CLASS'
        ),

        _class_variables: $ => choice(
            ...func_var_decls($),
            ...other_var_decls($)
        ),

        class_type_name: $ => $.identifier,

        instance_name: $ => prec(PREC.dereference, seq(
            field("qualifier", optional(seq($.namespace_qualifier, "::"))),
            field("name", $.instance),
            repeat1('^')
        )),

        instance: $ => $.identifier,

        interface_decl: $ => seq(
            'INTERFACE',
            field("name", $.identifier),
            field("directives", repeat($.using_directive)),
            field("extends", optional(seq('EXTENDS', $.interface_name_list))),
            field("prototype", repeat($.method_prototype)),
            'END_INTERFACE'
        ),

        method_prototype: $ => seq(
            'METHOD',
            field("name", $.identifier),
            field("data_type", optional(seq(':', $._data_type_access))),
            field("variables", repeat($._method_variables)),
            'END_METHOD'
        ),

        _method_variables: $ => choice(
            ...io_var_decls($)
        ),

        interface_spec_init: $ => seq(':=', $.interface_value),

        interface_value: $ => choice(
            $.symbolic_variable,
            $.instance_name,
            'NULL'
        ),

        interface_name_list: $ => commaSep1($.type_access),

        interface_name: $ => $.identifier,

        access_spec: $ => choice('PUBLIC', 'PROTECTED', 'PRIVATE', 'INTERNAL'),

        // Table 47 - Program declaration 

        prog_decl: $ => seq(
            'PROGRAM',
            field("name", $.identifier),
            field("declarations", repeat(choice(
                ...io_var_decls($),
                ...func_var_decls($),
                $.temp_var_decls,
                ...other_var_decls($),
                $.loc_var_decls,
                $.prog_access_decl
            ))),
            field("body", optional($.fb_body)),
            'END_PROGRAM'
        ),

        prog_type_access: $ => seq(
            field("qualifier", optional(seq($.namespace_qualifier, "::"))),
            $.identifier
        ),

        prog_access_decls: $ => seq(
            'ref_deref',
            repeat(seq($.prog_access_decl, ';')),
            $.identifier
        ),

        prog_access_decl: $ => seq(
            $.access_name,
            ':',
            $.symbolic_variable,
            optional($.multibit_part_access),
            ':',
            $._data_type_access,
            $.access_direction
        ),

        // Table 54 - 61 - Sequential Function Chart (SFC) 

        SFC: $ => repeat1($.SFC_network),

        SFC_network: $ => seq(
            $.initial_step,
            repeat(choice($.step, $.transition, $.action))
        ),

        initial_step: $ => seq(
            'INITIAL_STEP',
            $.step_name,
            ':',
            repeat(seq($.action_association, ';')),
            'END_STEP'
        ),

        step: $ => seq(
            'STEP',
            $.step_name,
            ':',
            repeat(seq($.action_association, ';')),
            'END_STEP'
        ),

        step_name: $ => $.identifier,

        action_association: $ => seq(
            $.action_name,
            '(',
            optional($.action_qualifier),
            repeat(seq(field("variable_name", $.identifier), ';')),
            ')'
        ),

        action_name: $ => $.identifier,

        action_qualifier: $ => choice(
            'N',
            'R',
            'S',
            'P',
            seq(
                choice('L', 'D', 'SD', 'DS', 'SL'),
                ',',
                $.action_time
            )
        ),

        action_time: $ => choice(
            $.duration,
            field("variable_name", $.identifier)
        ),

        transition: $ => seq(
            'TRANSITION',
            optional($.transition_name),
            ':',
            optional(seq('(', 'PRIORITY', ':=', $.unsigned_int, ')')),
            'FROM',
            $.steps,
            'TO',
            $.steps,
            $.transition_cond,
            'END_TRANSITION'
        ),

        transition_name: $ => $.identifier,

        steps: $ => choice(
            $.step_name,
            seq('(', $.step_name, repeat1(seq(',', $.step_name)), ')')
        ),

        transition_cond: $ => choice(
            seq(':=', $.expression, ';'),
            seq(':', choice($.fbd_network, $.ld_rung)
            )
        ),

        action: $ => seq(
            'ACTION',
            $.action_name,
            ':',
            $.fb_body,
            'END_ACTION'
        ),

        // Table 62 - Configuration and resource declaration 

        config_name: $ => $.identifier,

        resource_type_name: $ => $.identifier,

        config_decl: $ => seq(
            'CONFIGURATION',
            field("name", $.config_name),
            field("global_variables", optional($.global_var_decls)),
            field("ressources", choice($.single_resource_decl, repeat1($.resource_decl))),
            'END_CONFIGURATION'
        ),

        resource_decl: $ => seq(
            'RESOURCE',
            field("name", $.identifier),
            'ON',
            field("resource_type_name", $.resource_type_name),
            field("global_variables", optional($.global_var_decls)),
            field("ressource", $.single_resource_decl),
            'END_RESOURCE'
        ),

        single_resource_decl: $ => seq(
            repeat(seq($.task_config, ';')),
            repeat1(seq($.prog_config, ';'))
        ),

        access_decls: $ => seq(
            'ref_deref',
            repeat(seq($.access_decl, ';')),
            'END_VAR'
        ),

        access_decl: $ => seq(
            $.access_name,
            ':',
            $.access_path,
            ':',
            optional(seq($._data_type_access, $.access_direction)),
        ),

        access_path: $ => choice(
            seq(optional(seq($.identifier, '.')), $.direct_variable),
            seq(
                optional(seq($.identifier, '.')),
                repeat(seq(choice($.instance_name), '.')),
                $.symbolic_variable
            )
        ),

        global_ref_deref: $ => prec.left(seq(
            optional(seq($.identifier, '.')),
            field("name", $.identifier),
            optional(seq('.', field("struct_name", $.identifier)))
        )),

        access_name: $ => $.identifier,

        prog_output_access: $ => seq(
            field("prog_name", $.identifier),
            '.',
            $.symbolic_variable
        ),

        access_direction: $ => choice('READ_WRITE', 'READ_ONLY'),

        task_config: $ => seq(
            'TASK',
            field("name", $.identifier),
            field("init", $.task_init)
        ),

        task_init: $ => seq(
            '(',
            optional(seq('SINGLE', ':=', field("single", $.data_source), ',')),
            optional(seq('INTERVAL', ':=', field("interval", $.data_source), ',')),
            'PRIORITY', ':=', field("priority", $.unsigned_int),
            ')'
        ),

        data_source: $ => choice(
            $.constant,
            $.global_ref_deref,
            $.prog_output_access,
            $.direct_variable
        ),

        prog_config: $ => seq(
            'PROGRAM',
            field("retain", optional(choice('RETAIN', 'NON_RETAIN'))),
            field("name", $.identifier),
            field("task", optional(seq('WITH', $.identifier))),
            ':',
            field("access", $.prog_type_access),
            field("configuration_elements", optional(seq('(', $.prog_conf_elems, ')')))
        ),

        prog_conf_elems: $ => commaSep1($.prog_conf_elem),

        prog_conf_elem: $ => choice(
            $.fb_task,
            $.prog_cnxn
        ),

        fb_task: $ => seq(
            $.instance_name,
            'WITH',
            field("task", $.identifier)
        ),

        prog_cnxn: $ => choice(
            seq($.symbolic_variable, ':=', $.prog_data_source),
            seq($.symbolic_variable, '=>', $.data_sink),
        ),

        prog_data_source: $ => choice(
            $.constant,
            $.enum_value,
            $.global_ref_deref,
            $.direct_variable
        ),

        data_sink: $ => choice(
            $.global_ref_deref,
            $.direct_variable
        ),

        config_init: $ => seq(
            'VAR_CONFIG',
            repeat(seq($.config_inst_init, optional(';'))),
            'END_VAR'
        ),

        config_inst_init: $ => seq(
            field("resource", $.identifier),
            '.',
            field("prog", $.identifier),
            '.',
            repeat(seq(choice($.instance_name), '.')),
            choice(
                seq($.identifier, optional($.located_at), ':', $.loc_var_spec_init),
                seq(
                    $.instance_name,
                    ':',
                    $.type_access,
                    ':=',
                    $.struct_init
                )
            )
        ),

        // Table 64 - Namespace 

        namespace_decl: $ => seq(
            'NAMESPACE',
            field("internal", optional('INTERNAL')),
            field("name", $.namespace_h_name),
            field("directives", repeat($.using_directive)),
            field("elements", optional($.namespace_elements)),
            'END_NAMESPACE'
        ),

        namespace_elements: $ => repeat1(
            choice(
                $.data_type_decl,
                $.func_decl,
                $.fb_decl,
                $.class_decl,
                $.interface_decl,
                $.namespace_decl
            )
        ),

        namespace_h_name: $ => seq(
            $.identifier,
            repeat(seq('.', $.identifier))
        ),


        using_directive: $ => seq(
            'USING',
            commaSep1($.namespace_h_name),
            optional(';')
        ),

        pou_decl: $ => seq(
            repeat($.using_directive),
            repeat1(
                choice(
                    $.global_var_decls,
                    $.data_type_decl,
                    $.access_decls,
                    $.func_decl,
                    $.fb_decl,
                    $.class_decl,
                    $.interface_decl,
                    $.namespace_decl
                )
            )
        ),

        // Table 71 - 72 - Language Structured Text (ST) 

        expression: $ => choice(
            $.boolean_operator,
            $.comparison_operator,
            $.add_operator,
            $.mult_operator,
            $.power_operator,
            $.unary_operator,
            $.primary_expression
        ),

        primary_expression: $ => choice(
            $.constant,
            $.identifier,
            $.enum_value,
            $.variable_access,
            $.func_call,
            $.ref_value,
            $.parenthesized_expression
        ),

        parenthesized_expression: $ => seq('(', $.expression, ')'),

        boolean_operator: $ => choice(
            prec.left(PREC.boolean_or, seq($.expression, 'OR', $.expression)),
            prec.left(PREC.boolean_xor, seq($.expression, 'XOR', $.expression)),
            prec.left(PREC.boolean_and, seq($.expression, choice('&', 'AND'), $.expression))
        ),

        comparison_operator: $ => choice(
            prec.left(PREC.equality, seq($.expression, choice('=', '<>'), $.expression)),
            prec.left(PREC.comparison, seq($.expression, choice('<', '>', '<=', '>='), $.expression))
        ),

        add_operator: $ => prec.left(PREC.add,
            seq($.expression, choice('+', '-'), $.expression)
        ),

        mult_operator: $ => prec.left(PREC.modulo,
            seq($.expression, choice('*', '/', 'MOD'), $.expression)
        ),

        power_operator: $ => prec.right(PREC.exponentiation,
            seq($.expression, '**', $.expression)
        ),

        unary_operator: $ => prec(PREC.unary,
            seq(choice('-', '+', 'NOT'), $.expression)
        ),

        constant_expr: $ => $.constant,
        //todo: a constant expression must evaluate to a constant value at compile time 
        // for now we just accept any constant - but no expressions

        variable_access: $ => seq(
            $.variable,
            $.multibit_part_access
        ),

        multibit_part_access: $ => seq(
            '.',
            choice(
                $.unsigned_int,
                seq('%', optional(choice('X', 'B', 'W', 'D', 'L')), $.unsigned_int)
            )
        ),

        func_call: $ => seq(
            $.type_access,
            '(', prec(PREC.parameter_list, commaSep($.param_assign)), ')'
        ),

        stmt_list: $ => repeat1(seq($.stmt, optional(";"))),

        stmt: $ => choice(
            // assignments
            seq($.variable, ':=', $.expression),
            $.ref_assign,
            $.assignment_attempt,
            // subprog
            $.func_call,
            $.invocation,
            seq('SUPER', '(', ')'),
            'RETURN',
            // iteration
            $.if_stmt,
            $.case_stmt,
            $.for_stmt,
            $.while_stmt,
            $.repeat_stmt,
            // control flow
            'EXIT',
            'CONTINUE'
        ),

        assignment_attempt: $ => seq(
            choice($.ref_name, $.ref_deref),
            '?=',
            choice($.ref_name, $.ref_deref, $.ref_value)
        ),

        invocation: $ => seq(
            choice(
                $.instance_name,
                'THIS',
                seq(
                    optional(seq('THIS', '.')),
                    repeat1(seq(choice($.instance_name), '.')),
                    $.identifier
                )
            ),
            '(', commaSep($.param_assign), ')'
        ),

        param_assign: $ => choice(
            seq(optional(seq(field("variable_name", $.identifier), ':=')), $.expression),
            $.ref_assign,
            seq(optional('NOT'), field("variable_name", $.identifier), '=>', $.variable)
        ),

        if_stmt: $ => seq(
            'IF',
            $.expression,
            'THEN',
            optional($.stmt_list),
            repeat(seq('ELSE IF', $.expression, 'THEN', $.stmt_list)),
            optional(seq('ELSE', $.stmt_list)),
            'END_IF'
        ),

        case_stmt: $ => seq(
            'CASE',
            $.expression,
            'OF',
            repeat1($.case_selection),
            optional(seq('ELSE', $.stmt_list)),
            'END_CASE'
        ),

        case_selection: $ => seq(
            $.case_list,
            ':',
            $.stmt_list
        ),

        case_list: $ => commaSep1($.case_list_elem),

        case_list_elem: $ => choice(
            $.subrange,
            $.constant_expr
        ),

        for_stmt: $ => seq(
            "FOR",
            $.control_variable,
            ':=',
            $.for_list,
            'DO',
            $.stmt_list,
            'END_FOR'
        ),

        control_variable: $ => $.identifier,

        for_list: $ => seq(
            $.expression,
            'TO',
            $.expression,
            optional(seq('BY', $.expression))
        ),

        while_stmt: $ => seq(
            'WHILE',
            $.primary_expression,
            'DO',
            $.stmt_list,
            'END_WHILE'
        ),

        repeat_stmt: $ => seq(
            'REPEAT',
            $.stmt_list,
            'UNTIL',
            $.expression,
            'END_REPEAT'
        ),

        // Other

        namespace_qualifier: $ => prec.left(seq(
            "::", field("namespace", $.identifier),
            repeat(seq("::", field("namespace", $.identifier))),
        )),

        // Table 73 - 76 - Graphic languages elements 

        // note: IEC does not specify at least one occurence of a statement in a ladder diagram
        // but since tree sitter does not support empty string, we have to add at least one rung.
        ladder_diagram: $ => repeat1(
            $.ld_rung,
        ),

        ld_rung: $ => "todo_lad",

        // same
        fb_diagram: $ => repeat1(
            $.fbd_network,
        ),

        fbd_network: $ => "todo_fbd",

        // Table 1 - Character sets
        // Table 2 - Identifiers

        letter: $ => /[a-zA-Z_]/,
        bit: $ => /[01]/,
        octal_digit: $ => /[0-7]/,
        hex_digit: $ => /[0-9a-fA-F]/,
        identifier: $ => /[0-9a-zA-Z_]+/,
    }
});
